{"common": {"loading": "加载中...", "orContinueWith": "或继续使用", "error": "发生错误", "retry": "重试", "save": "保存", "ok": "确定", "delete": "删除", "edit": "编辑", "view": "查看", "create": "创建", "search": "搜索", "filter": "筛选", "sort": "排序", "settings": "设置", "profile": "个人资料", "logout": "退出登录", "language": "语言", "theme": "主题", "light": "明亮", "dark": "暗黑", "custom": "自定义", "open": "打开", "add": "添加", "actions": "操作", "close": "关闭", "reset": "重置", "menu": "菜单", "date": "日期", "dateRange": "日期范围", "columns": "列", "selectAll": "全选", "startDate": "开始日期", "endDate": "结束日期", "code": "促销代码", "name": "显示名称", "discountValue": "折扣值", "minimumOrderValue": "最低订单金额", "maximumDiscountAmount": "最大折扣金额", "status": "状态", "backToHome": "返回首页", "home": "首页", "componentsText": "组件", "viewExample": "查看示例", "animation": "动画", "products": "产品", "services": "服务", "service1": "服务 1", "service2": "服务 2", "service3": "服务 3", "contact": "联系我们", "inbox": "收件箱", "notifications": "通知", "help": "帮助与支持", "noResults": "未找到结果", "level": "等级", "activate": "激活", "deactivate": "停用", "details": "查看详情", "aiAgents": {"title": "AI 助手", "description": "管理您的 AI 助手和助理", "noAgentsFound": "未找到符合您搜索条件的助手"}, "selected": "已选择 {{count}} 项", "createOption": "创建 \"{{option}}\"", "primary": "主要", "secondary": "次要", "outline": "轮廓", "ghost": "幽灵", "success": "成功", "warning": "警告", "danger": "危险", "small": "小", "medium": "中", "large": "大", "disabled": "禁用", "createNew": "创建新的", "nextStep": "下一步", "cancel": "取消", "action": "操作", "ui": {"card": {"title": "卡片标题", "subtitle": "副标题", "content": "这是卡片的内容。", "footer": "页脚", "loading": "加载中...", "withFooter": {"content": "这张卡片有一个带有操作按钮的页脚。"}, "withIcon": {"content": "这张卡片在标题中有一个图标。"}, "bordered": {"content": "这张卡片有一个边框。"}, "customHeader": {"content": "这张卡片有一个自定义标题。"}}, "badge": {"new": "新", "count": "{{count}}"}, "tabs": {"tab": "标签 {{number}}", "content": "标签 {{number}} 内容"}, "accordion": {"item": "项目 {{number}}", "content": "项目 {{number}} 内容", "expandAll": "展开全部", "collapseAll": "收起全部"}}}, "auth": {"login": "登录", "register": "注册", "forgotPassword": "忘记密码", "resetPassword": "重置密码", "email": "电子邮箱", "password": "密码", "confirmPassword": "确认密码", "username": "用户名", "rememberMe": "记住我"}, "chat": {"newChat": "新对话", "sendMessage": "发送消息", "typeMessage": "输入消息...", "typeSlashForMenu": "输入 / 快速访问菜单...", "uploadFile": "上传文件", "uploadFromComputer": "从计算机上传", "uploadFromGoogleDrive": "从 Google Drive 添加", "webSearch": "网络搜索", "voiceInput": "语音输入", "aiAgents": "AI 助手", "aiAssistants": "AI 助理", "specializedAgents": "专业助手", "selectAgent": "选择助手", "maxFilesExceeded": "最多允许上传 {{max}} 个文件", "invalidFileTypes": "某些文件未上传，因为它们不受支持。允许的格式：图片、PDF、XLSX、CSV、DOCX、JSON、MD、JSONL", "errorProcessingFiles": "处理文件时出错。请重试。", "chooseFeature": "选择要使用的功能", "payment": "聊天机器人支付", "imagePasted": "已从剪贴板粘贴图片"}, "marketing": {"title": "营销", "description": "管理营销活动、广告和促销"}, "data": {"title": "数据与分析", "description": "查看和分析数据、报告和统计信息"}, "marketplace": {"title": "市场", "description": "探索和购买产品、服务和资源"}, "viewPanel": {"welcome": "欢迎使用 AI ERP", "recentChats": "最近对话", "favorites": "收藏", "history": "历史记录", "noData": "暂无数据", "loadMore": "加载更多"}, "settings": {"account": "账户", "appearance": "外观", "notifications": "通知", "privacy": "隐私", "language": "语言", "about": "关于"}, "errors": {"required": "此字段为必填项", "invalidEmail": "无效的电子邮箱地址", "passwordMismatch": "密码不匹配", "minLength": "最少 {{count}} 个字符", "maxLength": "最多 {{count}} 个字符", "serverError": "服务器错误，请稍后再试", "networkError": "网络错误，请检查您的连接"}, "components": {"library": {"title": "组件库", "description": "RedAI系统的共享组件库。组件采用现代风格设计，完全支持明/暗模式和响应式设计。"}, "charts": {"demo": {"title": "图表演示", "description": "具有响应式设计、多语言支持和主题兼容性的图表组件。"}, "lineChart": {"title": "折线图", "description": "折线图组件以线条形式显示数据，支持多条数据线、工具提示和图例。", "basic": {"title": "基本折线图", "description": "带有单条数据线的基本折线图。"}, "multiLine": {"title": "多线折线图", "description": "带有多条数据线的折线图。"}, "customized": {"title": "自定义折线图", "description": "带有自定义线型、线宽和点显示的折线图。"}}}, "menu": {"title": "菜单", "description": "具有多种功能的菜单：子菜单，不同模式，折叠状态", "horizontal": {"title": "水平菜单", "description": "水平显示的菜单"}, "vertical": {"title": "垂直菜单", "description": "垂直显示的菜单"}, "inline": {"title": "内联菜单", "description": "子菜单内联显示的菜单"}, "variants": {"title": "菜单变体", "description": "不同的菜单变体"}, "advanced": {"title": "高级菜单项", "description": "带有徽章和快捷键的菜单项"}}, "showCode": "显示代码", "hideCode": "隐藏代码", "copied": "已复制！", "copy": "复制", "categories": {"buttons": {"title": "按钮", "description": "不同类型的按钮：主要、次要、轮廓、图标按钮等..."}, "cards": {"title": "卡片", "description": "用于显示内容、信息、数据的不同类型的卡片..."}, "chips": {"title": "标签", "description": "标签是表示输入、属性或操作的紧凑元素..."}, "inputs": {"title": "输入框", "description": "不同类型的输入框：文本、数字、复选框、单选框、选择框等..."}, "layout": {"title": "布局组件", "description": "布局组件：容器、网格、弹性布局、调整器等..."}, "theme": {"title": "主题组件", "description": "与主题相关的组件：主题切换、语言选择器等...", "system": {"title": "主题系统", "description": "具有自定义和扩展功能的新主题系统"}}, "typography": {"title": "排版", "description": "用于在应用程序中保持一致文本样式的排版组件。"}, "form": {"title": "表单组件", "description": "用于构建具有各种布局和行为的表单的组件集合。", "theme": {"title": "主题系统表单", "description": "使用新主题系统的表单组件演示"}}}, "buttons": {"title": "按钮", "description": "RedAI系统中使用的不同类型的按钮。", "variants": {"title": "按钮变体", "description": "按钮变体：主要、次要、轮廓、成功、警告、危险"}, "sizes": {"title": "按钮尺寸", "description": "按钮尺寸：小、中、大"}, "withIcons": {"title": "带图标的按钮", "description": "左侧或右侧带有图标的按钮"}, "fullWidth": {"title": "全宽按钮", "description": "占据容器全宽的按钮", "button": "全宽按钮"}, "loading": {"title": "加载中按钮", "description": "加载状态的按钮"}, "disabled": {"title": "禁用按钮", "description": "禁用状态的按钮"}}, "animation": {"title": "动画", "description": "RedAI前端模板中可用的各种动画效果。", "fadeSlide": {"title": "淡入和滑动动画"}, "fadeIn": "淡入", "fadeInAnimation": "淡入动画", "slideIn": "滑入", "slideInAnimation": "滑入动画", "slideInLeft": "从左滑入", "slideInLeftAnimation": "从左滑入动画", "slideInRight": "从右滑入", "slideInRightAnimation": "从右滑入动画", "durationTiming": {"title": "持续时间和时机"}, "fast": "快速 (200ms)", "fastAnimation": "快速动画", "medium": "中等 (500ms)", "mediumAnimation": "中等动画", "slow": "慢速 (1000ms)", "slowAnimation": "慢速动画", "continuous": {"title": "连续动画"}, "pulse": "脉冲", "pulseAnimation": "脉冲动画", "spin": "旋转", "component": {"title": "组件动画"}, "tooltipContent": "这是带有动画的工具提示", "hoverMe": "悬停在我上面", "openModal": "打开模态框", "animatedModal": "动画模态框", "modalDescription": "此模态框有打开和关闭动画。", "infoNotification": "信息通知", "successNotification": "成功通知", "warningNotification": "警告通知", "errorNotification": "错误通知", "notificationDescription": "此通知有出现和消失动画。"}, "grid": {"title": "网格", "description": "网格组件帮助创建灵活和响应式的网格布局。", "basic": {"title": "基本网格", "description": "固定列数的网格"}, "responsive": {"title": "响应式网格", "description": "根据屏幕尺寸变化列数的网格"}, "gaps": {"title": "网格间距", "description": "元素之间具有不同间距的网格", "small": "小间距", "medium": "中间距", "large": "大间距"}}, "responsiveGrid": {"title": "响应式网格", "description": "根据屏幕尺寸和聊天面板状态自动调整的高级响应式网格。", "basic": {"title": "基本响应式网格", "description": "使用默认设置的响应式网格"}, "customColumns": {"title": "自定义列数", "description": "使用自定义列设置的响应式网格"}, "customGaps": {"title": "自定义间距", "description": "使用自定义间距设置的响应式网格"}, "withChatPanel": {"title": "与聊天面板配合", "description": "在聊天面板打开或关闭时调整的响应式网格"}, "realWorld": {"title": "实际应用示例", "description": "AI Agents模块中使用的响应式网格示例"}, "currentColumns": "当前列数", "chatPanel": "聊天面板", "open": "打开", "closed": "关闭", "openChatPanel": "打开聊天面板", "closeChatPanel": "关闭聊天面板", "props": "属性", "propName": "属性名", "propType": "类型", "propDefault": "默认值", "propDescription": "描述", "childrenDescription": "在网格中显示的子元素", "gapDescription": "网格项目之间的间距", "maxColumnsDescription": "聊天面板关闭时每个断点的最大列数", "maxColumnsWithChatPanelDescription": "聊天面板打开时每个断点的最大列数", "classNameDescription": "额外的CSS类", "onColumnsChangeDescription": "列数变化时的回调函数"}, "cards": {"title": "卡片", "description": "在RedAI系统中用于显示内容、信息、数据的不同类型的卡片。", "basic": {"title": "基本卡片", "description": "带有标题和内容的基本卡片"}, "withFooter": {"title": "带页脚的卡片", "description": "带有包含操作的页脚的卡片"}, "withIcon": {"title": "带图标的卡片", "description": "在标题中带有图标的卡片"}, "bordered": {"title": "带边框的卡片", "description": "周围有边框的卡片"}, "customHeader": {"title": "带自定义标题的卡片", "description": "带有自定义标题的卡片"}}, "typography": {"title": "排版", "description": "用于在应用程序中保持一致文本样式的排版组件。", "headings": {"title": "标题", "description": "从h1到h6的标题变体。"}, "body": {"title": "正文", "description": "用于段落和一般内容的正文文本变体。"}, "colors": {"title": "文本颜色", "description": "具有不同颜色选项的排版。"}, "weights": {"title": "字体粗细", "description": "具有不同字体粗细的排版。"}, "alignment": {"title": "文本对齐", "description": "具有不同文本对齐方式的排版。"}, "truncation": {"title": "文本截断", "description": "用于长文本的截断排版。"}, "responsive": {"title": "响应式排版", "description": "具有响应式字体大小的排版。"}}, "theme": {"title": "主题组件", "description": "RedAI系统中与主题相关的组件。", "toggle": {"title": "主题切换", "description": "在明亮模式和暗黑模式之间切换"}, "toggleSizes": {"title": "主题切换尺寸", "description": "不同尺寸的主题切换"}, "toggleCustomText": {"title": "自定义文本的主题切换", "description": "带有自定义文本的主题切换"}, "languageFlags": {"title": "语言国旗", "description": "显示支持的语言的国旗"}, "languageFlagSizes": {"title": "语言国旗尺寸", "description": "不同尺寸的语言国旗"}, "system": {"title": "主题系统", "description": "具有自定义和扩展功能的新主题系统", "variables": {"title": "CSS 变量", "description": "主题系统中使用的 CSS 变量", "primaryColor": "主要颜色", "primaryDescription": "使用 primary 和 primary-foreground 颜色", "secondaryColor": "次要颜色", "secondaryDescription": "使用 secondary 和 secondary-foreground 颜色", "accentColor": "强调颜色", "accentDescription": "使用 accent 和 accent-foreground 颜色", "successColor": "成功颜色", "successDescription": "使用 success 和 success-foreground 颜色", "warningColor": "警告颜色", "warningDescription": "使用 warning 和 warning-foreground 颜色", "errorColor": "错误颜色", "errorDescription": "使用 error 和 error-foreground 颜色"}, "cards": {"title": "卡片样式", "description": "使用新主题系统的卡片样式", "content": "使用主题变量的卡片内容", "mutedTitle": "柔和卡片", "mutedContent": "使用 card-muted 背景"}, "typography": {"title": "排版", "description": "使用新主题系统的排版"}}, "customizer": {"title": "主题定制器", "description": "允许用户自定义主题的组件", "open": "打开主题定制器", "mode": "主题模式", "primaryColor": "主要颜色", "secondaryColor": "次要颜色", "backgroundColor": "背景颜色", "textColor": "文本颜色", "borderRadius": "边框圆角"}, "languageFlagWithLabel": {"title": "带标签的语言国旗", "description": "带有语言名称标签的语言国旗"}}, "inputs": {"title": "输入框", "description": "RedAI系统中使用的不同类型的输入框。", "text": {"title": "文本输入框", "description": "用于文本输入的基本输入框"}, "password": {"title": "密码输入框", "description": "带有可见性切换的密码输入框"}, "helperText": {"title": "带帮助文本的输入框", "description": "下方带有辅助文本的输入框"}, "error": {"title": "带错误提示的输入框", "description": "处于错误状态的输入框"}, "disabled": {"title": "禁用的输入框", "description": "处于禁用状态的输入框"}, "withIcon": {"title": "带图标的输入框", "description": "左侧或右侧带有图标的输入框"}, "select": {"title": "选择框", "description": "具有多种功能的高级选择框组件", "newFormat": {"title": "选择框组件（新格式）", "description": "带有详细文档的选择框组件"}, "advanced": {"title": "高级选择框组件", "description": "异步、可创建、组合框和输入提示选择框组件"}, "advancedNew": {"title": "高级选择框组件（新格式）", "description": "带有详细文档的高级选择框组件"}, "usage": {"title": "选择框使用示例", "description": "在表单中使用选择框组件的实际示例"}}, "datepicker": {"title": "日期选择器组件", "description": "具有单日期和范围选择功能的高级日期选择器"}, "checkboxRadio": {"title": "复选框和单选框组件", "description": "复选框、复选框组、单选框和单选框组组件"}, "singleSelect": {"title": "单选选择框", "description": "允许选择单个值的选择框"}, "multiSelect": {"title": "多选选择框", "description": "允许选择多个值的选择框"}, "searchableSelect": {"title": "可搜索选择框", "description": "具有搜索功能的选择框"}, "groupedSelect": {"title": "分组选择框", "description": "具有分组选项的选择框"}, "labels": {"username": "用户名", "password": "密码", "email": "电子邮件", "search": "搜索", "website": "网站"}, "placeholders": {"username": "输入您的用户名", "password": "输入您的密码", "email": "输入您的电子邮件", "search": "搜索...", "website": "输入网站URL"}, "helpers": {"emailPrivacy": "我们绝不会与其他人分享您的电子邮件。"}, "errors": {"invalidEmail": "请输入有效的电子邮件地址"}, "loadingSelect": {"title": "加载中选择框", "description": "处于加载状态的选择框"}, "customRenderingSelect": {"title": "自定义渲染选择框", "description": "具有自定义选项渲染的选择框"}, "asyncSelect": {"title": "异步选择框", "description": "具有从API加载数据能力的选择框"}, "creatableSelect": {"title": "可创建选择框", "description": "允许在未找到选项时创建新选项的选择框"}, "comboboxSelect": {"title": "组合框选择框", "description": "结合下拉菜单和输入框，允许自由文本输入的选择框"}, "typeaheadSelect": {"title": "输入提示选择框", "description": "在输入时提供建议的选择框"}}, "layout": {"title": "布局组件", "description": "RedAI系统中用于组织内容的布局组件。", "container": {"title": "容器", "description": "容器限制内容宽度并使其居中"}, "grid": {"title": "网格", "description": "具有可自定义列的网格布局"}, "responsiveGrid": {"title": "响应式网格", "description": "根据屏幕尺寸变化列数的网格布局"}, "resizer": {"title": "调整器", "description": "允许在两个面板之间调整大小的组件"}, "horizontalResizer": {"title": "水平调整器", "description": "水平方向的调整器"}, "panels": {"left": "左侧面板", "right": "右侧面板", "top": "顶部面板", "bottom": "底部面板"}}, "form": {"title": "表单组件", "description": "用于构建具有各种布局和行为的表单的组件集合。", "basic": {"title": "基本表单", "description": "使用Zod模式进行验证的基本表单。", "example1": {"title": "基本表单", "description": "使用Zod模式进行基本验证的简单表单。"}, "example2": {"title": "高级验证表单", "description": "使用Zod模式进行高级验证规则的表单。"}, "fields": {"name": "姓名", "email": "电子邮箱", "username": "用户名", "password": "密码", "confirmPassword": "确认密码"}, "placeholders": {"name": "输入您的姓名", "email": "输入您的电子邮箱", "username": "输入用户名", "password": "输入密码", "confirmPassword": "确认密码"}, "helpTexts": {"agreeTerms": "我同意条款和条件"}, "buttons": {"submit": "提交", "register": "注册"}, "result": "结果", "reference": {"title": "表单组件参考", "form": {"title": "Form组件", "description": "管理表单状态和验证的主要组件。"}, "formItem": {"title": "FormItem组件", "description": "包装带有标签、错误消息和帮助文本的输入字段。"}, "input": {"title": "Input组件", "description": "具有各种选项的基本输入组件。"}}}, "dependencies": {"title": "表单字段依赖关系", "description": "创建级联下拉菜单等依赖字段"}, "templates": {"title": "表单模板", "description": "常见用例的即用型表单模板"}, "array": {"title": "表单数组", "description": "具有添加/删除功能的动态表单字段"}, "labels": {"name": "姓名", "email": "电子邮箱", "country": "国家", "gender": "性别", "agreeTerms": "我同意条款和条件", "username": "用户名", "password": "密码"}, "placeholders": {"name": "输入您的姓名", "email": "输入您的电子邮箱", "country": "选择您的国家", "password": "输入您的密码"}, "helpers": {"passwordLength": "必须至少8个字符"}, "errors": {"usernameTaken": "用户名已被使用", "selectCountry": "请选择一个国家"}, "countries": {"us": "美国", "uk": "英国", "ca": "加拿大", "au": "澳大利亚", "de": "德国", "fr": "法国", "jp": "日本", "cn": "中国", "in": "印度", "br": "巴西"}, "gender": {"male": "男", "female": "女", "other": "其他"}, "grid": {"title": "表单网格布局", "description": "以响应式网格布局组织表单字段。"}, "inline": {"title": "表单内联布局", "description": "为搜索或简单输入创建内联表单。"}, "horizontal": {"title": "表单水平布局", "description": "创建标签在左侧、字段在右侧的表单。"}, "layout": {"title": "表单布局", "description": "组织表单字段的不同布局选项。", "grid": {"title": "网格布局", "description": "以响应式网格布局组织表单字段。"}, "inline": {"title": "内联布局", "description": "为搜索栏和筛选器创建内联表单。"}, "horizontal": {"title": "水平布局", "description": "创建标签在左侧、字段在右侧的表单。"}, "fields": {"firstName": "名字", "lastName": "姓氏", "email": "电子邮箱", "phone": "电话", "address": "地址", "city": "城市", "state": "省/州", "zipCode": "邮政编码", "search": "搜索", "category": "类别", "username": "用户名", "password": "密码"}, "placeholders": {"firstName": "输入名字", "lastName": "输入姓氏", "email": "输入电子邮箱", "phone": "输入电话号码", "address": "输入地址", "city": "输入城市", "state": "输入省/州", "zipCode": "输入邮政编码", "search": "搜索...", "username": "输入用户名", "password": "输入密码"}, "options": {"allCategories": "所有类别", "products": "产品", "services": "服务", "blogs": "博客"}, "buttons": {"submit": "提交", "search": "搜索", "login": "登录"}, "result": "结果", "reference": {"title": "表单布局组件参考", "formGrid": {"title": "FormGrid组件", "description": "为表单字段创建响应式网格布局。"}, "formInline": {"title": "FormInline组件", "description": "为表单字段创建内联布局。"}, "formHorizontal": {"title": "FormHorizontal组件", "description": "创建标签在左侧、字段在右侧的水平布局。"}}}, "conditional": {"title": "条件字段", "description": "根据条件显示或隐藏字段。", "basic": {"title": "基本条件字段", "description": "基于简单条件显示字段。"}, "advanced": {"title": "高级条件字段", "description": "使用带有AND/OR逻辑的复杂条件。"}, "component": {"title": "ConditionalField组件", "description": "ConditionalField组件允许您根据条件显示或隐藏表单字段。它对于创建适应用户输入的动态表单非常有用。", "basicUsage": {"title": "基本用法", "description": "最常见的用例是基于简单条件显示字段："}, "conditionTypes": {"title": "条件类型", "description": "ConditionalField组件支持各种条件类型："}}, "complex": {"title": "复杂条件", "description": "ConditionalField组件支持使用AND和OR逻辑的复杂条件。这允许您创建复杂的表单行为。", "and": {"title": "AND逻辑", "description": "当您希望仅在满足多个条件时显示字段时，使用AND逻辑："}, "or": {"title": "OR逻辑", "description": "当您希望在满足任何条件时显示字段时，使用OR逻辑："}}, "fields": {"accountType": "账户类型", "name": "姓名", "email": "电子邮箱", "companyName": "公司名称", "taxId": "税号", "differentBillingAddress": "不同的账单地址？", "billingAddress": "账单地址", "city": "城市", "hasSpecialRequirements": "特殊要求？", "specialRequirements": "特殊要求", "contactMethod": "联系方式", "phoneNumber": "电话号码"}, "placeholders": {"name": "输入您的姓名", "email": "输入您的电子邮箱", "companyName": "输入公司名称", "taxId": "输入税号", "billingAddress": "输入账单地址", "city": "输入城市", "specialRequirements": "输入特殊要求", "phoneNumber": "输入电话号码"}, "options": {"personal": "个人", "business": "企业", "email": "电子邮箱", "phone": "电话", "both": "两者"}, "conditionTypes": {"equals": "字段值等于特定值", "notEquals": "字段值不等于特定值", "isTrue": "字段值为真（布尔字段）", "isFalse": "字段值为假（布尔字段）", "isEmpty": "字段值为空", "isNotEmpty": "字段值不为空"}, "examples": {"business": "仅当账户类型为\"企业\"时显示公司字段", "shipping": "仅当选中\"不同的送货地址\"时显示送货地址", "additional": "根据选择显示额外字段"}, "buttons": {"submit": "提交"}, "result": "结果"}, "apiForm": {"title": "API表单集成", "description": "使用useApiForm钩子将表单与API调用集成。"}, "apiError": {"title": "表单API错误处理", "description": "使用useFormErrors钩子处理表单中的API错误。"}, "theme": {"title": "主题系统表单", "description": "使用新主题系统的表单组件演示"}, "themeDemo": {"title": "主题系统表单组件", "description": "使用新主题系统的表单组件演示"}, "basicForm": {"title": "基本表单", "description": "具有各种输入类型的基本表单"}, "examples": {"title": "表单示例", "description": "常见用例的完整表单示例。", "login": {"title": "登录和注册表单", "description": "带有验证的完整登录和注册表单示例。"}, "multistep": {"title": "多步骤表单", "description": "带有进度跟踪和验证的多步骤表单示例。"}, "datepicker": {"title": "高级日期选择器示例", "description": "日期选择器的实际使用示例，包括工作日、假日和表单集成。"}, "checkboxRadio": {"title": "复选框和单选框示例", "description": "具有不同状态、尺寸和布局的复选框和单选框组件示例。"}}, "demo": {"title": "表单示例", "description": "使用React Hook Form和Zod进行验证的完整表单示例。", "result": "结果", "login": {"title": "登录表单", "description": "带有验证和错误处理的完整登录表单。", "emailHelp": "输入您注册的电子邮箱"}, "register": {"title": "注册表单", "description": "带有高级验证规则的完整注册表单。", "passwordHelp": "密码必须至少8个字符，包括大写字母、小写字母、数字和特殊字符", "confirmPasswordPlaceholder": "确认您的密码"}, "bestPractices": {"title": "表单最佳实践", "validation": {"title": "表单验证", "description": "始终在客户端和服务器端验证用户输入。", "item1": "使用Zod进行模式验证", "item2": "提供清晰的错误消息", "item3": "尽可能实时验证"}, "accessibility": {"title": "可访问性", "description": "确保您的表单对所有用户都可访问。", "item1": "为所有表单字段使用适当的标签", "item2": "确保键盘导航正常工作", "item3": "必要时使用ARIA属性"}, "ux": {"title": "用户体验", "description": "创建流畅的用户体验。", "item1": "在提交过程中显示加载状态", "item2": "当表单无效时禁用提交按钮", "item3": "提供清晰的成功和错误反馈"}}}, "validation": {"uppercase": "密码必须包含至少1个大写字母", "lowercase": "密码必须包含至少1个小写字母", "number": "密码必须包含至少1个数字", "special": "密码必须包含至少1个特殊字符", "agreeTerms": "您必须同意条款和条件"}, "variants": {"title": "表单组件变体", "description": "表单组件的不同变体", "inputError": {"title": "带错误的输入框"}, "inputHelper": {"title": "带帮助文本的输入框"}, "checkbox": {"title": "复选框变体", "default": "默认复选框", "rounded": "圆角复选框", "filled": "填充复选框", "outlined": "轮廓复选框"}, "radio": {"title": "单选框变体", "default": "默认单选框", "filled": "填充单选框", "outlined": "轮廓单选框"}, "selectError": {"title": "带错误的选择框"}}}, "formDependencies": {"title": "表单字段依赖关系", "description": "管理表单字段之间的依赖关系以创建动态表单。", "transform": {"title": "转换依赖", "description": "通过转换函数根据其他字段的值更新字段值。"}, "cascadingSelect": {"title": "级联选择", "description": "根据另一个选择框的值更新选择框选项。"}, "resetFields": {"title": "重置字段", "description": "当另一个字段更改时重置字段值。"}}, "tooltip": {"title": "工具提示", "description": "现代工具提示组件，具有各种位置和样式。", "basic": {"title": "基本工具提示", "description": "具有默认设置的基本工具提示。", "content": "这是一个工具提示", "button": "悬停在我上面"}, "positions": {"title": "工具提示位置", "description": "工具提示可以显示在不同位置。", "top": "顶部工具提示", "topButton": "顶部", "right": "右侧工具提示", "rightButton": "右侧", "bottom": "底部工具提示", "bottomButton": "底部", "left": "左侧工具提示", "leftButton": "左侧"}, "variants": {"title": "工具提示变体", "description": "具有不同样式的工具提示。", "dark": {"content": "深色工具提示", "button": "深色"}, "light": {"content": "浅色工具提示", "button": "浅色"}}, "sizes": {"title": "工具提示尺寸", "description": "不同尺寸的工具提示。", "small": {"content": "小型工具提示"}, "medium": {"content": "中型工具提示"}, "large": {"content": "大型工具提示"}}, "withIcons": {"title": "带图标的工具提示", "description": "与图标按钮一起使用的工具提示。", "add": "添加新项目"}, "noArrow": {"title": "无箭头工具提示", "description": "可以显示没有箭头的工具提示。", "content": "无箭头工具提示", "button": "无箭头"}}, "searchBar": {"title": "搜索栏", "description": "具有动画和各种样式的现代搜索栏组件。", "basic": {"title": "基本搜索栏", "description": "具有切换功能的基本搜索栏。"}, "variants": {"title": "搜索栏变体", "description": "具有不同样式的搜索栏。"}, "animation": {"title": "搜索栏动画", "description": "具有切换动画的搜索栏。"}, "withoutClear": {"title": "无清除按钮的搜索栏", "description": "没有清除按钮的搜索栏。"}, "customWidth": {"title": "自定义宽度的搜索栏", "description": "具有自定义最大宽度的搜索栏。"}, "show": "显示", "hide": "隐藏"}, "modernMenu": {"title": "现代菜单", "description": "具有各种样式和位置的现代菜单组件。", "basic": {"title": "基本菜单", "description": "具有默认设置的基本菜单。"}, "withIcons": {"title": "带图标的菜单", "description": "带有图标的菜单项。"}, "placement": {"title": "菜单位置", "description": "具有不同位置的菜单。", "top": "顶部", "right": "右侧", "bottom": "底部", "left": "左侧"}}, "chips": {"title": "标签", "description": "标签组件用于显示表示输入、属性或操作的紧凑元素。", "basic": {"title": "基本标签", "description": "具有不同变体的基本标签。"}, "outlined": {"title": "轮廓标签", "description": "具有不同变体的轮廓标签。"}, "sizes": {"title": "标签尺寸", "description": "不同尺寸的标签。"}, "icons": {"title": "带图标的标签", "description": "左侧和右侧带有图标的标签。"}, "closable": {"title": "可关闭标签", "description": "带有关闭按钮的标签。"}, "clickable": {"title": "可点击标签", "description": "可以点击的标签。"}, "disabled": {"title": "禁用标签", "description": "处于禁用状态的标签。"}, "avatar": {"title": "带头像的标签", "description": "带有头像的标签。"}, "loading": {"title": "加载中标签", "description": "处于加载状态的标签。"}, "selected": {"title": "已选择标签", "description": "处于已选择状态的标签。"}, "animation": {"title": "动画标签", "description": "带有动画效果的标签。"}, "group": {"title": "标签组", "description": "具有各种功能的标签组。", "basic": "基本标签组", "closable": "可关闭标签组", "multiSelect": "多选标签组", "animated": "动画标签组"}}, "formSections": {"title": "表单分区", "description": "将表单分成多个区域，以便更好地组织和使用。", "collapsible": {"title": "可折叠分区", "description": "可以展开或折叠的分区，以节省空间并专注于当前任务。"}, "nestedSections": {"title": "嵌套分区", "description": "用于组织复杂表单的嵌套分区。"}, "customStyling": {"title": "自定义样式", "description": "通过className属性自定义分区样式。"}, "variants": {"title": "分区变体", "description": "FormSection的不同变体。", "default": "默认", "defaultDesc": "这是默认变体", "bordered": "带边框", "borderedDesc": "这有更粗的边框", "elevated": "阴影效果", "elevatedDesc": "这有阴影效果", "gradient": "渐变背景", "gradientDesc": "这有渐变背景"}, "sizes": {"title": "分区尺寸", "description": "FormSection的不同尺寸。", "small": "小", "smallDesc": "这是一个小尺寸分区", "medium": "中", "mediumDesc": "这是一个中等尺寸分区（默认）", "large": "大", "largeDesc": "这是一个大尺寸分区"}, "animation": {"title": "动画", "description": "打开/关闭分区时的动画效果。", "fade": "淡入淡出", "fadeDesc": "打开/关闭时的淡入淡出效果", "slide": "滑动", "slideDesc": "打开/关闭时的上下滑动效果", "both": "两者结合", "bothDesc": "淡入淡出和滑动效果的组合"}, "accordion": {"title": "手风琴", "description": "一次只能打开一个分区。", "group1": "分组 1", "group2": "分组 2", "firstSection": "手风琴中的第一个分区", "secondSection": "手风琴中的第二个分区", "thirdSection": "手风琴中的第三个分区"}, "badge": {"title": "徽章", "description": "在标题旁边显示徽章。", "new": "新", "required": "必填", "optional": "可选", "stringBadgeTitle": "带字符串徽章的区域", "stringBadgeDesc": "此区域有一个字符串徽章", "customBadgeTitle": "带自定义徽章的区域", "customBadgeDesc": "此区域有一个自定义徽章组件"}, "iconPosition": {"title": "图标位置", "description": "标题中图标的位置。", "left": "左侧", "leftDesc": "图标在左侧", "right": "右侧", "rightDesc": "图标在右侧（默认）"}, "fields": {"firstName": "名字", "lastName": "姓氏", "email": "电子邮箱", "phone": "电话号码", "companyName": "公司名称", "jobTitle": "职位", "department": "部门", "address": "地址", "city": "城市", "state": "州/省", "zipCode": "邮编", "country": "国家", "cardNumber": "卡号", "cardName": "持卡人姓名", "cardExpiry": "有效期", "cardCvv": "安全码", "saveCard": "保存卡信息", "receiveNewsletter": "接收新闻通讯"}, "placeholders": {"firstName": "输入名字", "lastName": "输入姓氏", "email": "<EMAIL>", "phone": "输入电话号码", "companyName": "输入公司名称", "jobTitle": "输入职位", "department": "输入部门"}, "buttons": {"submit": "提交"}, "result": "结果", "personalInfo": "个人信息", "personalInfoDesc": "输入您的个人信息", "companyInfo": "公司信息", "companyInfoDesc": "输入您的公司信息（如有）", "formSectionComponent": "FormSection 组件", "formSectionDesc": "FormSection组件用于将表单字段分组为多个区域，帮助组织表单，使其更清晰、更易于使用。", "features": "功能", "featuresList": {"groupFields": "将表单字段分组为多个区域", "collapsible": "支持可折叠（可以打开/关闭）", "titleDesc": "显示区域的标题和描述", "variants": "多种变体：默认、带边框、阴影效果、渐变背景", "sizes": "多种尺寸：小、中、大", "animation": "打开/关闭时的动画效果：淡入淡出、滑动、两者结合", "accordion": "支持手风琴模式（一次只能打开一个区域）", "badge": "支持在标题旁边显示徽章", "iconPosition": "自定义图标位置：左侧、右侧", "customStyle": "通过className自定义样式", "darkMode": "完全支持明/暗模式", "responsive": "在所有屏幕尺寸上都响应式"}, "props": "属性", "propsList": {"title": "区域的标题", "description": "区域的描述（可选）", "collapsible": "是否可以打开/关闭区域（默认：false）", "defaultExpanded": "区域的默认状态（默认：true）", "variant": "区域的变体（默认、带边框、阴影效果、渐变背景）", "size": "区域的尺寸（小、中、大）", "icon": "标题的自定义图标", "iconPosition": "图标的位置（左侧、右侧）", "badge": "显示在标题旁边的徽章", "animated": "是否在打开/关闭时使用动画", "animationType": "动画类型（淡入淡出、滑动、两者结合）", "animationDuration": "动画持续时间（毫秒）", "accordionId": "手风琴组的ID", "id": "区域的ID（用于手风琴）", "onExpandChange": "打开/关闭状态变化时的回调", "className": "区域的附加类", "titleClassName": "标题的附加类", "descriptionClassName": "描述的附加类", "contentClassName": "内容的附加类", "headerClassName": "标题栏的附加类"}, "collapsibleSections": "可折叠分区", "collapsibleDesc": "FormSection支持可折叠功能，可以节省空间并帮助用户专注于当前任务。", "usage": "使用方法", "usageSteps": {"addProp": "添加collapsible属性到FormSection", "defaultState": "使用defaultExpanded设置默认状态", "trackChanges": "使用onExpandChange跟踪状态变化"}, "managingSections": "管理多个分区", "managingSectionsDesc": "要管理多个分区的打开/关闭状态，您可以：", "managingStepsList": {"stateObject": "在state对象中存储状态", "functions": "创建打开/关闭所有分区的函数", "callback": "使用onExpandChange回调更新状态"}, "gridIntegration": "与FormGrid集成", "gridIntegrationDesc": "FormSection可以与FormGrid结合使用，为表单创建更复杂的布局。", "advantages": "优势", "advantagesList": {"gridLayout": "按网格布局组织字段", "responsive": "在不同屏幕尺寸上响应式", "columns": "轻松调整列数和间距"}, "gridUsage": "使用方法", "gridUsageSteps": {"placeGrid": "在FormSection内放置FormGrid", "setColumns": "设置列数和间距", "spanColumns": "使用className=\"col-span-2\"让一个字段占据多列"}, "customStylingTitle": "自定义样式", "customStylingDesc": "FormSection提供多个属性来自定义区域的不同部分的样式。", "stylingProps": "样式属性", "stylingPropsList": {"className": "自定义整个区域的样式", "titleClassName": "自定义标题的样式", "descriptionClassName": "自定义描述的样式", "contentClassName": "自定义内容的样式"}, "examples": "示例", "examplesList": {"primaryBorder": "添加主色边框：className=\"border-2 border-primary\"", "titleColor": "更改标题颜色：titleClassName=\"text-primary\"", "italicDesc": "斜体描述：descriptionClassName=\"italic\"", "contentBg": "更改内容背景：contentClassName=\"bg-gray-50\""}, "variantsTitle": "变体", "variantsDesc": "FormSection支持多种变体以适应应用程序的设计。", "variantsList": {"default": "默认变体，带有细边框", "bordered": "带有更粗边框的变体", "elevated": "带有阴影效果的变体", "gradient": "带有渐变背景的变体"}, "variantUsage": "使用方法", "badgeAndIcon": "徽章和图标位置", "badgeDesc": "FormSection支持在标题旁边显示徽章，以表示状态或提供额外信息。", "badgeFeatures": {"stringNumber": "徽章可以是字符串、数字或ReactNode", "defaultBadge": "字符串和数字将在默认Badge组件中显示", "customBadge": "ReactNode允许完全自定义徽章"}, "iconPositionDesc": "FormSection允许自定义打开/关闭图标的位置。", "iconPositions": {"right": "右侧（默认）", "left": "左侧"}, "expandAll": "展开全部", "collapseAll": "收起全部", "newsletterSubscription": "新闻通讯订阅", "managePreferences": "管理您的新闻通讯偏好", "sizesDesc": "FormSection支持多种尺寸以适应应用程序的设计。", "sizesTitle": "尺寸", "sizesList": {"small": "小尺寸，适合紧凑型表单", "medium": "中等尺寸（默认）", "large": "大尺寸，适合需要强调的表单"}, "sizeUsage": "使用size属性选择尺寸", "animationDesc": "FormSection支持多种动画效果，用于打开/关闭分区。", "animationTypes": "动画类型", "animationTypesList": {"fade": "打开/关闭时的淡入淡出效果", "slide": "打开/关闭时的上下滑动效果", "both": "淡入淡出和滑动效果的组合"}, "animationUsage": "使用animated和animationType属性", "accordionDesc": "FormSection支持手风琴模式，一次只允许打开一个分区。", "howItWorks": "工作原理", "accordionWorkingList": {"oneAtTime": "同一手风琴组中的分区在另一个分区打开时会关闭", "uniqueId": "每个分区需要一个唯一ID", "sameAccordionId": "同一组中的分区需要相同的accordionId"}, "accordionUsage": "使用accordionId和id属性"}}}